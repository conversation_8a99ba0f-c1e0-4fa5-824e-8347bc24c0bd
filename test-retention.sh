#!/bin/bash

# Test retention policy in dry-run mode
# Replace YOUR_API_URL and YOUR_AUTH_TOKEN with actual values

API_URL="http://localhost:8000"  # or your actual API URL
AUTH_TOKEN="your-auth-token-here"

echo "Testing retention policy (dry run)..."
curl -X POST "${API_URL}/api/retention/metadata/cron" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${AUTH_TOKEN}" \
  -d '{
    "dry_run": true,
    "batch_size": 100
  }' | jq .

echo -e "\n\nTesting retention policy for specific org (dry run)..."
curl -X POST "${API_URL}/api/retention/metadata/cron" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${AUTH_TOKEN}" \
  -d '{
    "dry_run": true,
    "batch_size": 100,
    "org_id": "your-org-id-here"
  }' | jq .

echo -e "\n\nTo run actual deletion (remove dry_run or set to false):"
echo "curl -X POST \"${API_URL}/api/retention/metadata/cron\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer ${AUTH_TOKEN}\" \\"
echo "  -d '{ \"dry_run\": false, \"batch_size\": 100 }'"
